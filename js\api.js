// API Client para comunicação com o backend
class ApiClient {
    constructor() {
        this.baseURL = window.location.origin;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}/api${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    // Métodos para Clientes
    async getClientes() {
        return this.request('/clientes');
    }

    async createCliente(cliente) {
        return this.request('/clientes', {
            method: 'POST',
            body: cliente
        });
    }

    async updateCliente(id, cliente) {
        return this.request(`/clientes/${id}`, {
            method: 'PUT',
            body: cliente
        });
    }

    async deleteCliente(id) {
        return this.request(`/clientes/${id}`, {
            method: 'DELETE'
        });
    }

    // Métodos para Produtos
    async getProdutos() {
        return this.request('/produtos');
    }

    async createProduto(produto) {
        return this.request('/produtos', {
            method: 'POST',
            body: produto
        });
    }

    async updateProduto(id, produto) {
        return this.request(`/produtos/${id}`, {
            method: 'PUT',
            body: produto
        });
    }

    async deleteProduto(id) {
        return this.request(`/produtos/${id}`, {
            method: 'DELETE'
        });
    }

    // Métodos para Funcionários
    async getFuncionarios() {
        return this.request('/funcionarios');
    }

    async createFuncionario(funcionario) {
        return this.request('/funcionarios', {
            method: 'POST',
            body: funcionario
        });
    }

    async updateFuncionario(id, funcionario) {
        return this.request(`/funcionarios/${id}`, {
            method: 'PUT',
            body: funcionario
        });
    }

    async deleteFuncionario(id) {
        return this.request(`/funcionarios/${id}`, {
            method: 'DELETE'
        });
    }

    // Métodos para Agendamentos
    async getAgendamentos() {
        return this.request('/agendamentos');
    }

    async createAgendamento(agendamento) {
        return this.request('/agendamentos', {
            method: 'POST',
            body: agendamento
        });
    }

    async updateAgendamento(id, agendamento) {
        return this.request(`/agendamentos/${id}`, {
            method: 'PUT',
            body: agendamento
        });
    }

    async updateAgendamentoStatus(id, status) {
        return this.request(`/agendamentos/${id}/status`, {
            method: 'PUT',
            body: { status }
        });
    }

    async deleteAgendamento(id) {
        return this.request(`/agendamentos/${id}`, {
            method: 'DELETE'
        });
    }

    // Dashboard
    async getDashboardStats() {
        return this.request('/dashboard/stats');
    }
}

// Instância global da API
const api = new ApiClient();

// Utilitários para formatação
const formatters = {
    currency: (value) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value || 0);
    },

    date: (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    },

    datetime: (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('pt-BR');
    },

    time: (timeString) => {
        if (!timeString) return '';
        return timeString.substring(0, 5); // HH:MM
    },

    phone: (phone) => {
        if (!phone) return '';
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length === 11) {
            return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
        }
        if (cleaned.length === 10) {
            return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 6)}-${cleaned.substring(6)}`;
        }
        return phone;
    }
};

// Sistema de notificações avançado
class NotificationSystem {
    constructor() {
        this.container = document.getElementById('notifications-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notifications-container';
            document.body.appendChild(this.container);
        }
        this.notifications = new Map();
        this.maxNotifications = 5;
    }

    show(message, type = 'info', options = {}) {
        const {
            title = '',
            duration = 5000,
            persistent = false,
            actions = []
        } = options;

        // Remover notificações antigas se exceder o limite
        if (this.notifications.size >= this.maxNotifications) {
            const oldestId = this.notifications.keys().next().value;
            this.hide(oldestId);
        }

        const id = Date.now().toString();
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.dataset.id = id;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = `
                <div class="notification-actions">
                    ${actions.map(action => `
                        <button class="btn btn-sm btn-secondary" onclick="${action.handler}">
                            ${action.label}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
                ${actionsHtml}
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // Mostrar notificação com animação
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });

        // Configurar botão de fechar
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.hide(id);
        });

        // Auto-remover após duração especificada (se não for persistente)
        if (!persistent && duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }

    hideAll() {
        this.notifications.forEach((_, id) => this.hide(id));
    }

    success(message, options = {}) {
        return this.show(message, 'success', {
            title: 'Sucesso!',
            duration: 4000,
            ...options
        });
    }

    error(message, options = {}) {
        return this.show(message, 'error', {
            title: 'Erro!',
            duration: 8000,
            ...options
        });
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', {
            title: 'Atenção!',
            duration: 6000,
            ...options
        });
    }

    info(message, options = {}) {
        return this.show(message, 'info', {
            title: 'Informação',
            duration: 5000,
            ...options
        });
    }

    confirm(message, onConfirm, onCancel = null) {
        return this.show(message, 'warning', {
            title: 'Confirmação',
            persistent: true,
            actions: [
                {
                    label: 'Cancelar',
                    handler: `notifications.hide('${Date.now()}'); ${onCancel ? onCancel : ''}`
                },
                {
                    label: 'Confirmar',
                    handler: `notifications.hide('${Date.now()}'); ${onConfirm}`
                }
            ]
        });
    }
}

// Instância global do sistema de notificações
const notifications = new NotificationSystem();

// Utilitários para modais
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// Fechar modal ao clicar fora
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal')) {
        closeModal(e.target.id);
    }
});

// Fechar modal com ESC
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            closeModal(openModal.id);
        }
    }
});

// Loading state
function setLoading(element, isLoading) {
    if (isLoading) {
        element.disabled = true;
        element.innerHTML = `
            <span class="loading">
                <div class="loading-spinner"></div>
                Carregando...
            </span>
        `;
    } else {
        element.disabled = false;
        // Restaurar conteúdo original (deve ser implementado conforme necessário)
    }
}

// Validação de formulários
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });
    
    return isValid;
}

// Debounce para pesquisas
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
