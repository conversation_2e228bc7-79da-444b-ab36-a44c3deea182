<!-- Página de Agendamentos -->
<div class="page-header">
    <div>
        <h1 class="page-title">Agendamentos</h1>
        <p class="page-subtitle">Gerencie todos os agendamentos de visitas</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="openModal('novoAgendamentoModal')">
            <i class="fas fa-plus"></i>
            Novo Agendamento
        </button>
    </div>
</div>

<!-- Filtros -->
<div class="filters-container">
    <div class="filters-row">
        <div class="filter-group">
            <label class="form-label">Status</label>
            <select class="form-control" id="filtroStatus">
                <option value="">Todos os status</option>
                <option value="agendado">Agendado</option>
                <option value="confirmado">Confirmado</option>
                <option value="em_andamento">Em Andamento</option>
                <option value="concluido">Concluído</option>
                <option value="cancelado">Cancelado</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="form-label">Funcionário</label>
            <select class="form-control" id="filtroFuncionario">
                <option value="">Todos os funcionários</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="form-label">Data Inicial</label>
            <input type="date" class="form-control" id="filtroDataInicial">
        </div>
        <div class="filter-group">
            <label class="form-label">Data Final</label>
            <input type="date" class="form-control" id="filtroDataFinal">
        </div>
        <div class="filter-group">
            <label class="form-label">&nbsp;</label>
            <button class="btn btn-secondary" onclick="limparFiltros()">
                <i class="fas fa-eraser"></i>
                Limpar Filtros
            </button>
        </div>
    </div>
</div>

<!-- Visualização em Calendário/Lista -->
<div class="card mb-4">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h3 class="card-title">Agendamentos</h3>
            <div class="tabs-nav">
                <button class="tab-button active" onclick="alternarVisualizacao('calendario')">
                    <i class="fas fa-calendar"></i>
                    Calendário
                </button>
                <button class="tab-button" onclick="alternarVisualizacao('lista')">
                    <i class="fas fa-list"></i>
                    Lista
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Visualização Calendário -->
        <div id="visualizacao-calendario" class="tab-content active">
            <div id="calendar-agendamentos"></div>
        </div>
        
        <!-- Visualização Lista -->
        <div id="visualizacao-lista" class="tab-content">
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Cliente</th>
                            <th>Funcionário</th>
                            <th>Produto/Serviço</th>
                            <th>Data/Hora</th>
                            <th>Status</th>
                            <th>Valor</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="agendamentos-tbody">
                        <!-- Dados serão carregados dinamicamente -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detalhes do Agendamento -->
<div id="detalhesAgendamentoModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Detalhes do Agendamento</h2>
            <button class="modal-close" onclick="closeModal('detalhesAgendamentoModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="detalhes-agendamento-content">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('detalhesAgendamentoModal')">
                Fechar
            </button>
            <button type="button" class="btn btn-primary" onclick="editarAgendamento()">
                <i class="fas fa-edit"></i>
                Editar
            </button>
        </div>
    </div>
</div>

<!-- Modal Editar Agendamento -->
<div id="editarAgendamentoModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Editar Agendamento</h2>
            <button class="modal-close" onclick="closeModal('editarAgendamentoModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="editarAgendamentoForm">
                <input type="hidden" name="id" id="editAgendamentoId">
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Cliente</label>
                        <select class="form-control" name="cliente_id" required>
                            <option value="">Selecione um cliente</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Funcionário</label>
                        <select class="form-control" name="funcionario_id" required>
                            <option value="">Selecione um funcionário</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Produto/Serviço</label>
                        <select class="form-control" name="produto_id" required>
                            <option value="">Selecione um produto/serviço</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select class="form-control" name="status" required>
                            <option value="agendado">Agendado</option>
                            <option value="confirmado">Confirmado</option>
                            <option value="em_andamento">Em Andamento</option>
                            <option value="concluido">Concluído</option>
                            <option value="cancelado">Cancelado</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Data da Visita</label>
                        <input type="date" class="form-control" name="data_visita" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Hora de Início</label>
                        <input type="time" class="form-control" name="hora_inicio" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Hora de Fim</label>
                        <input type="time" class="form-control" name="hora_fim" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Valor</label>
                    <input type="number" class="form-control" name="valor" step="0.01" placeholder="0,00">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Endereço da Visita</label>
                    <textarea class="form-control" name="endereco_visita" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Observações</label>
                    <textarea class="form-control" name="observacoes" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('editarAgendamentoModal')">
                Cancelar
            </button>
            <button type="submit" form="editarAgendamentoForm" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Salvar Alterações
            </button>
        </div>
    </div>
</div>

<script>
// Funções específicas da página de agendamentos
function alternarVisualizacao(tipo) {
    // Atualizar botões
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Atualizar conteúdo
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`visualizacao-${tipo}`).classList.add('active');
    
    if (tipo === 'calendario') {
        // Inicializar calendário de agendamentos se necessário
        if (!window.calendarAgendamentos) {
            window.calendarAgendamentos = new Calendar('calendar-agendamentos');
        }
    } else {
        // Carregar lista de agendamentos
        carregarListaAgendamentos();
    }
}

function carregarListaAgendamentos() {
    const tbody = document.getElementById('agendamentos-tbody');
    if (!tbody) return;
    
    // Aplicar filtros
    let agendamentos = app.data.agendamentos;
    
    const filtroStatus = document.getElementById('filtroStatus').value;
    const filtroFuncionario = document.getElementById('filtroFuncionario').value;
    const filtroDataInicial = document.getElementById('filtroDataInicial').value;
    const filtroDataFinal = document.getElementById('filtroDataFinal').value;
    
    if (filtroStatus) {
        agendamentos = agendamentos.filter(a => a.status === filtroStatus);
    }
    
    if (filtroFuncionario) {
        agendamentos = agendamentos.filter(a => a.funcionario_id == filtroFuncionario);
    }
    
    if (filtroDataInicial) {
        agendamentos = agendamentos.filter(a => a.data_visita >= filtroDataInicial);
    }
    
    if (filtroDataFinal) {
        agendamentos = agendamentos.filter(a => a.data_visita <= filtroDataFinal);
    }
    
    // Ordenar por data/hora
    agendamentos.sort((a, b) => {
        const dateA = new Date(`${a.data_visita} ${a.hora_inicio}`);
        const dateB = new Date(`${b.data_visita} ${b.hora_inicio}`);
        return dateB - dateA; // Mais recentes primeiro
    });
    
    tbody.innerHTML = agendamentos.map(agendamento => `
        <tr>
            <td>${agendamento.cliente_nome || 'N/A'}</td>
            <td>${agendamento.funcionario_nome || 'N/A'}</td>
            <td>${agendamento.produto_nome || 'N/A'}</td>
            <td>
                ${formatters.date(agendamento.data_visita)}<br>
                <small>${formatters.time(agendamento.hora_inicio)} - ${formatters.time(agendamento.hora_fim)}</small>
            </td>
            <td>
                <span class="badge badge-${app.getStatusBadgeClass(agendamento.status)}">
                    ${app.getStatusLabel(agendamento.status)}
                </span>
            </td>
            <td>${formatters.currency(agendamento.valor)}</td>
            <td>
                <button class="btn btn-sm btn-secondary" onclick="verDetalhesAgendamento(${agendamento.id})" title="Ver detalhes">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-primary" onclick="editarAgendamentoModal(${agendamento.id})" title="Editar">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function limparFiltros() {
    document.getElementById('filtroStatus').value = '';
    document.getElementById('filtroFuncionario').value = '';
    document.getElementById('filtroDataInicial').value = '';
    document.getElementById('filtroDataFinal').value = '';
    carregarListaAgendamentos();
}

function verDetalhesAgendamento(id) {
    const agendamento = app.data.agendamentos.find(a => a.id === id);
    if (!agendamento) return;
    
    const content = document.getElementById('detalhes-agendamento-content');
    content.innerHTML = `
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Cliente</label>
                <div class="form-control" readonly>${agendamento.cliente_nome || 'N/A'}</div>
            </div>
            <div class="form-group">
                <label class="form-label">Funcionário</label>
                <div class="form-control" readonly>${agendamento.funcionario_nome || 'N/A'}</div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Produto/Serviço</label>
                <div class="form-control" readonly>${agendamento.produto_nome || 'N/A'}</div>
            </div>
            <div class="form-group">
                <label class="form-label">Status</label>
                <span class="badge badge-${app.getStatusBadgeClass(agendamento.status)}">
                    ${app.getStatusLabel(agendamento.status)}
                </span>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Data da Visita</label>
                <div class="form-control" readonly>${formatters.date(agendamento.data_visita)}</div>
            </div>
            <div class="form-group">
                <label class="form-label">Horário</label>
                <div class="form-control" readonly>${formatters.time(agendamento.hora_inicio)} - ${formatters.time(agendamento.hora_fim)}</div>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Valor</label>
            <div class="form-control" readonly>${formatters.currency(agendamento.valor)}</div>
        </div>
        <div class="form-group">
            <label class="form-label">Endereço da Visita</label>
            <div class="form-control" readonly style="min-height: 60px;">${agendamento.endereco_visita || 'Não informado'}</div>
        </div>
        <div class="form-group">
            <label class="form-label">Observações</label>
            <div class="form-control" readonly style="min-height: 60px;">${agendamento.observacoes || 'Nenhuma observação'}</div>
        </div>
    `;
    
    openModal('detalhesAgendamentoModal');
}

// Configurar filtros quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    // Configurar eventos dos filtros
    ['filtroStatus', 'filtroFuncionario', 'filtroDataInicial', 'filtroDataFinal'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', carregarListaAgendamentos);
        }
    });
});
</script>
