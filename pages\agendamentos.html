<!-- Página de Agendamentos -->
<div class="page-header">
    <div>
        <h1 class="page-title">Agendamentos</h1>
        <p class="page-subtitle">Gerencie todos os agendamentos de visitas</p>
    </div>
    <div style="display: flex; gap: 1rem;">
        <button class="btn btn-secondary" onclick="exportarAgendamentos()">
            <i class="fas fa-download"></i>
            Exportar
        </button>
        <button class="btn btn-primary" onclick="openModal('novoAgendamentoModal')">
            <i class="fas fa-plus"></i>
            Novo Agendamento
        </button>
    </div>
</div>

<!-- Estatísticas Rápidas -->
<div class="stats-grid" style="margin-bottom: 2rem;">
    <div class="stat-card">
        <div class="stat-value" id="totalAgendamentos">0</div>
        <div class="stat-label">Total de Agendamentos</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="agendamentosConfirmados">0</div>
        <div class="stat-label">Confirmados</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="agendamentosPendentes">0</div>
        <div class="stat-label">Pendentes</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="receitaTotal">R$ 0</div>
        <div class="stat-label">Receita Total</div>
    </div>
</div>

<!-- Filtros Avançados -->
<div class="filters-container">
    <div class="filters-row">
        <div class="filter-group">
            <label class="filter-label">Buscar</label>
            <input type="text" class="filter-input" id="filtroBusca" placeholder="Nome do cliente, produto...">
        </div>
        <div class="filter-group">
            <label class="filter-label">Status</label>
            <select class="filter-input" id="filtroStatus">
                <option value="">Todos os status</option>
                <option value="agendado">Agendado</option>
                <option value="confirmado">Confirmado</option>
                <option value="em_andamento">Em Andamento</option>
                <option value="concluido">Concluído</option>
                <option value="cancelado">Cancelado</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Funcionário</label>
            <select class="filter-input" id="filtroFuncionario">
                <option value="">Todos os funcionários</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Data Inicial</label>
            <input type="date" class="filter-input" id="filtroDataInicial">
        </div>
        <div class="filter-group">
            <label class="filter-label">Data Final</label>
            <input type="date" class="filter-input" id="filtroDataFinal">
        </div>
        <div class="filter-group">
            <button class="btn btn-primary" onclick="aplicarFiltros()">
                <i class="fas fa-search"></i>
                Filtrar
            </button>
            <button class="btn btn-secondary" onclick="limparFiltros()">
                <i class="fas fa-times"></i>
                Limpar
            </button>
        </div>
    </div>
</div>

<!-- Visualização em Abas -->
<div class="tabs">
    <div class="tabs-nav">
        <button class="tab-button active" onclick="alterarVisualizacao('lista')">
            <i class="fas fa-list"></i> Lista
        </button>
        <button class="tab-button" onclick="alterarVisualizacao('calendario')">
            <i class="fas fa-calendar"></i> Calendário
        </button>
        <button class="tab-button" onclick="alterarVisualizacao('kanban')">
            <i class="fas fa-columns"></i> Kanban
        </button>
    </div>
</div>

<!-- Visualização Lista -->
<div id="visualizacao-lista" class="tab-content active">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Lista de Agendamentos</h3>
            <div style="display: flex; gap: 1rem; align-items: center;">
                <span id="contadorAgendamentos" style="color: var(--gray-600); font-size: 0.875rem;">0 agendamentos</span>
                <div class="dropdown">
                    <button class="btn btn-sm btn-secondary dropdown-toggle">
                        <i class="fas fa-sort"></i> Ordenar
                    </button>
                    <div class="dropdown-menu">
                        <div class="dropdown-item" onclick="ordenarPor('data_visita')">Data da Visita</div>
                        <div class="dropdown-item" onclick="ordenarPor('cliente_nome')">Nome do Cliente</div>
                        <div class="dropdown-item" onclick="ordenarPor('status')">Status</div>
                        <div class="dropdown-item" onclick="ordenarPor('valor')">Valor</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Data/Hora</th>
                            <th>Cliente</th>
                            <th>Funcionário</th>
                            <th>Produto/Serviço</th>
                            <th>Valor</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody id="agendamentosTableBody">
                        <!-- Dados serão carregados dinamicamente -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Visualização Calendário -->
<div id="visualizacao-calendario" class="tab-content">
    <div class="card">
        <div class="card-body">
            <div id="calendar-agendamentos-container">
                <!-- Calendário será renderizado aqui -->
            </div>
        </div>
    </div>
</div>

<!-- Visualização Kanban -->
<div id="visualizacao-kanban" class="tab-content">
    <div class="kanban-board">
        <div class="kanban-column">
            <div class="kanban-header">
                <h4>Agendado</h4>
                <span class="kanban-count" id="count-agendado">0</span>
            </div>
            <div class="kanban-cards" id="kanban-agendado"></div>
        </div>
        <div class="kanban-column">
            <div class="kanban-header">
                <h4>Confirmado</h4>
                <span class="kanban-count" id="count-confirmado">0</span>
            </div>
            <div class="kanban-cards" id="kanban-confirmado"></div>
        </div>
        <div class="kanban-column">
            <div class="kanban-header">
                <h4>Em Andamento</h4>
                <span class="kanban-count" id="count-em_andamento">0</span>
            </div>
            <div class="kanban-cards" id="kanban-em_andamento"></div>
        </div>
        <div class="kanban-column">
            <div class="kanban-header">
                <h4>Concluído</h4>
                <span class="kanban-count" id="count-concluido">0</span>
            </div>
            <div class="kanban-cards" id="kanban-concluido"></div>
        </div>
    </div>
</div>

<!-- Modal Detalhes do Agendamento -->
<div id="detalhesAgendamentoModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Detalhes do Agendamento</h2>
            <button class="modal-close" onclick="closeModal('detalhesAgendamentoModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="detalhes-agendamento-content">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('detalhesAgendamentoModal')">
                Fechar
            </button>
            <button type="button" class="btn btn-primary" onclick="editarAgendamento()">
                <i class="fas fa-edit"></i>
                Editar
            </button>
        </div>
    </div>
</div>

<!-- Modal Editar Agendamento -->
<div id="editarAgendamentoModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Editar Agendamento</h2>
            <button class="modal-close" onclick="closeModal('editarAgendamentoModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="editarAgendamentoForm">
                <input type="hidden" name="id" id="editAgendamentoId">
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Cliente</label>
                        <select class="form-control" name="cliente_id" required>
                            <option value="">Selecione um cliente</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Funcionário</label>
                        <select class="form-control" name="funcionario_id" required>
                            <option value="">Selecione um funcionário</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Produto/Serviço</label>
                        <select class="form-control" name="produto_id" required>
                            <option value="">Selecione um produto/serviço</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select class="form-control" name="status" required>
                            <option value="agendado">Agendado</option>
                            <option value="confirmado">Confirmado</option>
                            <option value="em_andamento">Em Andamento</option>
                            <option value="concluido">Concluído</option>
                            <option value="cancelado">Cancelado</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Data da Visita</label>
                        <input type="date" class="form-control" name="data_visita" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Hora de Início</label>
                        <input type="time" class="form-control" name="hora_inicio" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Hora de Fim</label>
                        <input type="time" class="form-control" name="hora_fim" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Valor</label>
                    <input type="number" class="form-control" name="valor" step="0.01" placeholder="0,00">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Endereço da Visita</label>
                    <textarea class="form-control" name="endereco_visita" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Observações</label>
                    <textarea class="form-control" name="observacoes" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('editarAgendamentoModal')">
                Cancelar
            </button>
            <button type="submit" form="editarAgendamentoForm" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Salvar Alterações
            </button>
        </div>
    </div>
</div>

<script>
// Variáveis globais da página
let agendamentosFiltrados = [];
let ordenacaoAtual = { campo: 'data_visita', direcao: 'desc' };
let calendarAgendamentos = null;

// Funções específicas da página de agendamentos
function alterarVisualizacao(tipo) {
    // Atualizar botões
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Atualizar conteúdo
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`visualizacao-${tipo}`).classList.add('active');

    // Carregar conteúdo específico
    switch(tipo) {
        case 'lista':
            carregarListaAgendamentos();
            break;
        case 'calendario':
            if (!calendarAgendamentos) {
                calendarAgendamentos = new Calendar('calendar-agendamentos-container');
            }
            break;
        case 'kanban':
            carregarKanban();
            break;
    }
}

function aplicarFiltros() {
    const busca = document.getElementById('filtroBusca').value.toLowerCase();
    const status = document.getElementById('filtroStatus').value;
    const funcionario = document.getElementById('filtroFuncionario').value;
    const dataInicial = document.getElementById('filtroDataInicial').value;
    const dataFinal = document.getElementById('filtroDataFinal').value;

    agendamentosFiltrados = app.data.agendamentos.filter(agendamento => {
        // Filtro de busca
        if (busca && !agendamento.cliente_nome.toLowerCase().includes(busca) &&
            !agendamento.produto_nome.toLowerCase().includes(busca)) {
            return false;
        }

        // Filtro de status
        if (status && agendamento.status !== status) return false;

        // Filtro de funcionário
        if (funcionario && agendamento.funcionario_id != funcionario) return false;

        // Filtro de data inicial
        if (dataInicial && agendamento.data_visita < dataInicial) return false;

        // Filtro de data final
        if (dataFinal && agendamento.data_visita > dataFinal) return false;

        return true;
    });

    // Aplicar ordenação
    aplicarOrdenacao();

    // Atualizar visualização ativa
    const visualizacaoAtiva = document.querySelector('.tab-content.active').id;
    if (visualizacaoAtiva === 'visualizacao-lista') {
        renderizarTabela();
    } else if (visualizacaoAtiva === 'visualizacao-kanban') {
        carregarKanban();
    }

    // Atualizar estatísticas
    atualizarEstatisticas();
}

function carregarListaAgendamentos() {
    agendamentosFiltrados = [...app.data.agendamentos];
    aplicarOrdenacao();
    renderizarTabela();
    atualizarEstatisticas();
}

function renderizarTabela() {
    const tbody = document.getElementById('agendamentosTableBody');
    if (!tbody) return;

    if (agendamentosFiltrados.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center" style="padding: 2rem; color: var(--gray-500);">
                    Nenhum agendamento encontrado
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = agendamentosFiltrados.map(agendamento => `
        <tr>
            <td>
                <div style="font-weight: 600;">${formatters.date(agendamento.data_visita)}</div>
                <div style="font-size: 0.875rem; color: var(--gray-600);">
                    ${formatters.time(agendamento.hora_inicio)} - ${formatters.time(agendamento.hora_fim)}
                </div>
            </td>
            <td>${agendamento.cliente_nome}</td>
            <td>${agendamento.funcionario_nome}</td>
            <td>${agendamento.produto_nome}</td>
            <td>${formatters.currency(agendamento.valor)}</td>
            <td>
                <span class="badge badge-${getStatusBadgeClass(agendamento.status)}">
                    ${getStatusLabel(agendamento.status)}
                </span>
            </td>
            <td>
                <div class="dropdown">
                    <button class="btn btn-sm btn-secondary dropdown-toggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu">
                        <div class="dropdown-item" onclick="editarAgendamento(${agendamento.id})">
                            <i class="fas fa-edit"></i> Editar
                        </div>
                        <div class="dropdown-item" onclick="alterarStatus(${agendamento.id}, 'confirmado')">
                            <i class="fas fa-check"></i> Confirmar
                        </div>
                        <div class="dropdown-item" onclick="alterarStatus(${agendamento.id}, 'cancelado')">
                            <i class="fas fa-times"></i> Cancelar
                        </div>
                        <div class="dropdown-item" onclick="excluirAgendamento(${agendamento.id})">
                            <i class="fas fa-trash"></i> Excluir
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    `).join('');

    // Configurar dropdowns
    configurarDropdowns();

    // Atualizar contador
    const contador = document.getElementById('contadorAgendamentos');
    if (contador) {
        contador.textContent = `${agendamentosFiltrados.length} agendamento${agendamentosFiltrados.length !== 1 ? 's' : ''}`;
    }
}

function configurarDropdowns() {
    document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            const dropdown = toggle.closest('.dropdown');
            const isOpen = dropdown.classList.contains('open');

            // Fechar todos os dropdowns
            document.querySelectorAll('.dropdown').forEach(d => d.classList.remove('open'));

            // Abrir o dropdown clicado se não estava aberto
            if (!isOpen) {
                dropdown.classList.add('open');
            }
        });
    });

    // Fechar dropdowns ao clicar fora
    document.addEventListener('click', () => {
        document.querySelectorAll('.dropdown').forEach(d => d.classList.remove('open'));
    });
}

function ordenarPor(campo) {
    if (ordenacaoAtual.campo === campo) {
        ordenacaoAtual.direcao = ordenacaoAtual.direcao === 'asc' ? 'desc' : 'asc';
    } else {
        ordenacaoAtual.campo = campo;
        ordenacaoAtual.direcao = 'asc';
    }

    aplicarOrdenacao();
    renderizarTabela();
}

function aplicarOrdenacao() {
    agendamentosFiltrados.sort((a, b) => {
        let valorA, valorB;

        switch(ordenacaoAtual.campo) {
            case 'data_visita':
                valorA = new Date(`${a.data_visita} ${a.hora_inicio}`);
                valorB = new Date(`${b.data_visita} ${b.hora_inicio}`);
                break;
            case 'cliente_nome':
                valorA = a.cliente_nome.toLowerCase();
                valorB = b.cliente_nome.toLowerCase();
                break;
            case 'status':
                valorA = a.status;
                valorB = b.status;
                break;
            case 'valor':
                valorA = parseFloat(a.valor) || 0;
                valorB = parseFloat(b.valor) || 0;
                break;
            default:
                return 0;
        }

        if (valorA < valorB) return ordenacaoAtual.direcao === 'asc' ? -1 : 1;
        if (valorA > valorB) return ordenacaoAtual.direcao === 'asc' ? 1 : -1;
        return 0;
    });
}

function limparFiltros() {
    document.getElementById('filtroBusca').value = '';
    document.getElementById('filtroStatus').value = '';
    document.getElementById('filtroFuncionario').value = '';
    document.getElementById('filtroDataInicial').value = '';
    document.getElementById('filtroDataFinal').value = '';

    agendamentosFiltrados = [...app.data.agendamentos];
    aplicarOrdenacao();

    const visualizacaoAtiva = document.querySelector('.tab-content.active').id;
    if (visualizacaoAtiva === 'visualizacao-lista') {
        renderizarTabela();
    } else if (visualizacaoAtiva === 'visualizacao-kanban') {
        carregarKanban();
    }

    atualizarEstatisticas();
}

function atualizarEstatisticas() {
    const total = agendamentosFiltrados.length;
    const confirmados = agendamentosFiltrados.filter(a => a.status === 'confirmado').length;
    const pendentes = agendamentosFiltrados.filter(a => a.status === 'agendado').length;
    const receita = agendamentosFiltrados
        .filter(a => ['confirmado', 'concluido'].includes(a.status))
        .reduce((sum, a) => sum + (parseFloat(a.valor) || 0), 0);

    document.getElementById('totalAgendamentos').textContent = total;
    document.getElementById('agendamentosConfirmados').textContent = confirmados;
    document.getElementById('agendamentosPendentes').textContent = pendentes;
    document.getElementById('receitaTotal').textContent = formatters.currency(receita);
}

function carregarKanban() {
    const statusColumns = ['agendado', 'confirmado', 'em_andamento', 'concluido'];

    statusColumns.forEach(status => {
        const container = document.getElementById(`kanban-${status}`);
        const countElement = document.getElementById(`count-${status}`);

        if (!container || !countElement) return;

        const agendamentosStatus = agendamentosFiltrados.filter(a => a.status === status);
        countElement.textContent = agendamentosStatus.length;

        container.innerHTML = agendamentosStatus.map(agendamento => `
            <div class="kanban-card status-${agendamento.status}" onclick="editarAgendamento(${agendamento.id})">
                <div class="kanban-card-header">
                    <div class="kanban-card-title">${agendamento.cliente_nome}</div>
                    <div class="kanban-card-time">${formatters.time(agendamento.hora_inicio)}</div>
                </div>
                <div class="kanban-card-info">
                    <div><i class="fas fa-calendar"></i> ${formatters.date(agendamento.data_visita)}</div>
                    <div><i class="fas fa-user"></i> ${agendamento.funcionario_nome}</div>
                    <div><i class="fas fa-box"></i> ${agendamento.produto_nome}</div>
                </div>
                <div class="kanban-card-value">${formatters.currency(agendamento.valor)}</div>
                <div class="kanban-card-actions">
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); editarAgendamento(${agendamento.id})" title="Editar">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); alterarStatus(${agendamento.id}, 'confirmado')" title="Confirmar">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </div>
        `).join('');
    });
}

function verDetalhesAgendamento(id) {
    const agendamento = app.data.agendamentos.find(a => a.id === id);
    if (!agendamento) return;
    
    const content = document.getElementById('detalhes-agendamento-content');
    content.innerHTML = `
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Cliente</label>
                <div class="form-control" readonly>${agendamento.cliente_nome || 'N/A'}</div>
            </div>
            <div class="form-group">
                <label class="form-label">Funcionário</label>
                <div class="form-control" readonly>${agendamento.funcionario_nome || 'N/A'}</div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Produto/Serviço</label>
                <div class="form-control" readonly>${agendamento.produto_nome || 'N/A'}</div>
            </div>
            <div class="form-group">
                <label class="form-label">Status</label>
                <span class="badge badge-${app.getStatusBadgeClass(agendamento.status)}">
                    ${app.getStatusLabel(agendamento.status)}
                </span>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label class="form-label">Data da Visita</label>
                <div class="form-control" readonly>${formatters.date(agendamento.data_visita)}</div>
            </div>
            <div class="form-group">
                <label class="form-label">Horário</label>
                <div class="form-control" readonly>${formatters.time(agendamento.hora_inicio)} - ${formatters.time(agendamento.hora_fim)}</div>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">Valor</label>
            <div class="form-control" readonly>${formatters.currency(agendamento.valor)}</div>
        </div>
        <div class="form-group">
            <label class="form-label">Endereço da Visita</label>
            <div class="form-control" readonly style="min-height: 60px;">${agendamento.endereco_visita || 'Não informado'}</div>
        </div>
        <div class="form-group">
            <label class="form-label">Observações</label>
            <div class="form-control" readonly style="min-height: 60px;">${agendamento.observacoes || 'Nenhuma observação'}</div>
        </div>
    `;
    
    openModal('detalhesAgendamentoModal');
}

function editarAgendamento(id) {
    if (calendar) {
        calendar.editAgendamento(id);
    }
}

async function alterarStatus(id, novoStatus) {
    try {
        await api.updateAgendamentoStatus(id, novoStatus);

        // Atualizar dados locais
        const agendamento = app.data.agendamentos.find(a => a.id === id);
        if (agendamento) {
            agendamento.status = novoStatus;
        }

        // Atualizar visualização
        aplicarFiltros();

        // Atualizar calendário se existir
        if (calendar) {
            calendar.loadAgendamentos();
        }

        notifications.success(`Status alterado para: ${getStatusLabel(novoStatus)}`);
    } catch (error) {
        console.error('Erro ao alterar status:', error);
        notifications.error('Erro ao alterar status do agendamento');
    }
}

function excluirAgendamento(id) {
    const agendamento = app.data.agendamentos.find(a => a.id === id);
    if (!agendamento) return;

    notifications.confirm(
        `Tem certeza que deseja excluir o agendamento de ${agendamento.cliente_nome}?`,
        `confirmarExclusao(${id})`,
        null
    );
}

async function confirmarExclusao(id) {
    try {
        await api.deleteAgendamento(id);

        // Remover dos dados locais
        app.data.agendamentos = app.data.agendamentos.filter(a => a.id !== id);

        // Atualizar visualização
        aplicarFiltros();

        // Atualizar calendário se existir
        if (calendar) {
            calendar.loadAgendamentos();
        }

        notifications.success('Agendamento excluído com sucesso!');
    } catch (error) {
        console.error('Erro ao excluir agendamento:', error);
        notifications.error('Erro ao excluir agendamento');
    }
}

function exportarAgendamentos() {
    const dados = agendamentosFiltrados.map(agendamento => ({
        'Data': formatters.date(agendamento.data_visita),
        'Hora Início': formatters.time(agendamento.hora_inicio),
        'Hora Fim': formatters.time(agendamento.hora_fim),
        'Cliente': agendamento.cliente_nome,
        'Funcionário': agendamento.funcionario_nome,
        'Produto/Serviço': agendamento.produto_nome,
        'Valor': agendamento.valor,
        'Status': getStatusLabel(agendamento.status),
        'Endereço': agendamento.endereco_visita || '',
        'Observações': agendamento.observacoes || ''
    }));

    // Converter para CSV
    const headers = Object.keys(dados[0] || {});
    const csvContent = [
        headers.join(','),
        ...dados.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    // Download do arquivo
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `agendamentos_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function getStatusBadgeClass(status) {
    const statusMap = {
        'agendado': 'info',
        'confirmado': 'success',
        'em_andamento': 'warning',
        'concluido': 'success',
        'cancelado': 'error'
    };
    return statusMap[status] || 'info';
}

function getStatusLabel(status) {
    const statusMap = {
        'agendado': 'Agendado',
        'confirmado': 'Confirmado',
        'em_andamento': 'Em Andamento',
        'concluido': 'Concluído',
        'cancelado': 'Cancelado'
    };
    return statusMap[status] || status;
}

// Configurar filtros quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    // Configurar eventos dos filtros
    const filtros = ['filtroBusca', 'filtroStatus', 'filtroFuncionario', 'filtroDataInicial', 'filtroDataFinal'];
    filtros.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (element.type === 'text') {
                element.addEventListener('input', debounce(aplicarFiltros, 300));
            } else {
                element.addEventListener('change', aplicarFiltros);
            }
        }
    });

    // Configurar datas padrão (mês atual)
    const hoje = new Date();
    const primeiroDiaMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    const ultimoDiaMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0);

    const filtroDataInicial = document.getElementById('filtroDataInicial');
    const filtroDataFinal = document.getElementById('filtroDataFinal');

    if (filtroDataInicial) {
        filtroDataInicial.value = primeiroDiaMes.toISOString().split('T')[0];
    }

    if (filtroDataFinal) {
        filtroDataFinal.value = ultimoDiaMes.toISOString().split('T')[0];
    }
});
</script>
