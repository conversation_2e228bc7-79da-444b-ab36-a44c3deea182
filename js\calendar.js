// Sistema de Calendário
class Calendar {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentDate = new Date();
        this.agendamentos = [];
        this.selectedDate = null;
        
        this.monthNames = [
            'Janeiro', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON>', 'Agos<PERSON>', 'Setem<PERSON>', 'Out<PERSON><PERSON>', 'Novembro', 'Dezembro'
        ];
        
        this.dayNames = ['Dom', 'Seg', 'Ter', '<PERSON>ua', 'Qui', 'Sex', '<PERSON>áb'];
        
        this.init();
    }

    init() {
        this.render();
        this.loadAgendamentos();
    }

    render() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        this.container.innerHTML = `
            <div class="calendar-container">
                <div class="calendar-header">
                    <div class="calendar-nav">
                        <button class="calendar-nav-btn" onclick="calendar.previousMonth()">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h3 class="calendar-title">${this.monthNames[month]} ${year}</h3>
                        <button class="calendar-nav-btn" onclick="calendar.nextMonth()">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="calendar-nav">
                        <button class="calendar-nav-btn" onclick="calendar.goToToday()">
                            <i class="fas fa-calendar-day"></i>
                            Hoje
                        </button>
                    </div>
                </div>
                <div class="calendar-grid">
                    ${this.renderDayHeaders()}
                    ${this.renderDays()}
                </div>
            </div>
        `;
    }

    renderDayHeaders() {
        return this.dayNames.map(day => 
            `<div class="calendar-day-header">${day}</div>`
        ).join('');
    }

    renderDays() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        const days = [];
        const today = new Date();
        
        for (let i = 0; i < 42; i++) {
            const currentDay = new Date(startDate);
            currentDay.setDate(startDate.getDate() + i);
            
            const isCurrentMonth = currentDay.getMonth() === month;
            const isToday = currentDay.toDateString() === today.toDateString();
            const dayAgendamentos = this.getAgendamentosForDate(currentDay);
            
            let dayClass = 'calendar-day';
            if (!isCurrentMonth) dayClass += ' other-month';
            if (isToday) dayClass += ' today';
            
            days.push(`
                <div class="${dayClass}" data-date="${currentDay.toISOString().split('T')[0]}" onclick="calendar.selectDate('${currentDay.toISOString().split('T')[0]}')">
                    <div class="calendar-day-number">${currentDay.getDate()}</div>
                    ${this.renderDayEvents(dayAgendamentos)}
                </div>
            `);
        }
        
        return days.join('');
    }

    renderDayEvents(agendamentos) {
        if (!agendamentos || agendamentos.length === 0) return '';
        
        return agendamentos.slice(0, 3).map(agendamento => {
            const statusClass = `status-${agendamento.status}`;
            const time = formatters.time(agendamento.hora_inicio);
            return `
                <div class="calendar-event ${statusClass}" onclick="event.stopPropagation(); calendar.viewAgendamento(${agendamento.id})" title="${agendamento.cliente_nome} - ${agendamento.produto_nome}">
                    ${time} ${agendamento.cliente_nome}
                </div>
            `;
        }).join('') + (agendamentos.length > 3 ? `<div class="calendar-event">+${agendamentos.length - 3} mais</div>` : '');
    }

    getAgendamentosForDate(date) {
        const dateStr = date.toISOString().split('T')[0];
        return this.agendamentos.filter(agendamento => 
            agendamento.data_visita === dateStr
        );
    }

    async loadAgendamentos() {
        try {
            this.agendamentos = await api.getAgendamentos();
            this.render();
        } catch (error) {
            console.error('Erro ao carregar agendamentos:', error);
            notifications.error('Erro ao carregar agendamentos do calendário');
        }
    }

    previousMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        this.render();
    }

    nextMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        this.render();
    }

    goToToday() {
        this.currentDate = new Date();
        this.render();
    }

    selectDate(dateStr) {
        this.selectedDate = dateStr;
        
        // Remover seleção anterior
        this.container.querySelectorAll('.calendar-day.selected').forEach(day => {
            day.classList.remove('selected');
        });
        
        // Adicionar seleção atual
        const dayElement = this.container.querySelector(`[data-date="${dateStr}"]`);
        if (dayElement) {
            dayElement.classList.add('selected');
        }
        
        // Mostrar agendamentos do dia
        this.showDayAgendamentos(dateStr);
    }

    showDayAgendamentos(dateStr) {
        const agendamentos = this.getAgendamentosForDate(new Date(dateStr));
        const date = new Date(dateStr);
        const formattedDate = formatters.date(dateStr);

        // Criar modal para mostrar agendamentos do dia
        const modalId = 'dayAgendamentosModal';
        let modal = document.getElementById(modalId);

        if (!modal) {
            modal = document.createElement('div');
            modal.id = modalId;
            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Agendamentos - ${formattedDate}</h2>
                    <button class="modal-close" onclick="closeModal('${modalId}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${agendamentos.length === 0 ?
                        '<p class="text-center" style="color: var(--gray-500); padding: 2rem;">Nenhum agendamento para este dia</p>' :
                        `<div class="agendamentos-list">
                            ${agendamentos.map(agendamento => `
                                <div class="agendamento-card" style="border: 1px solid var(--gray-200); border-radius: var(--border-radius); padding: 1rem; margin-bottom: 1rem;">
                                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 0.5rem;">
                                        <div>
                                            <h4 style="margin: 0; color: var(--gray-900);">${agendamento.cliente_nome}</h4>
                                            <p style="margin: 0; color: var(--gray-600); font-size: 0.875rem;">${agendamento.produto_nome}</p>
                                        </div>
                                        <span class="badge badge-${this.getStatusBadgeClass(agendamento.status)}">${this.getStatusLabel(agendamento.status)}</span>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.875rem; color: var(--gray-600);">
                                        <div><i class="fas fa-clock"></i> ${formatters.time(agendamento.hora_inicio)} - ${formatters.time(agendamento.hora_fim)}</div>
                                        <div><i class="fas fa-user"></i> ${agendamento.funcionario_nome}</div>
                                        <div><i class="fas fa-dollar-sign"></i> ${formatters.currency(agendamento.valor)}</div>
                                        <div><i class="fas fa-map-marker-alt"></i> ${agendamento.endereco_visita ? 'Endereço definido' : 'Sem endereço'}</div>
                                    </div>
                                    ${agendamento.observacoes ? `<div style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid var(--gray-200); font-size: 0.875rem; color: var(--gray-600);">${agendamento.observacoes}</div>` : ''}
                                    <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                                        <button class="btn btn-sm btn-primary" onclick="calendar.editAgendamento(${agendamento.id})">
                                            <i class="fas fa-edit"></i> Editar
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="calendar.updateStatus(${agendamento.id}, 'confirmado')">
                                            <i class="fas fa-check"></i> Confirmar
                                        </button>
                                        <button class="btn btn-sm btn-secondary" onclick="calendar.updateStatus(${agendamento.id}, 'cancelado')">
                                            <i class="fas fa-times"></i> Cancelar
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>`
                    }
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="calendar.newAgendamentoForDate('${dateStr}')">
                        <i class="fas fa-plus"></i> Novo Agendamento
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal('${modalId}')">
                        Fechar
                    </button>
                </div>
            </div>
        `;

        openModal(modalId);
    }

    viewAgendamento(id) {
        const agendamento = this.agendamentos.find(a => a.id === id);
        if (agendamento) {
            this.editAgendamento(id);
        }
    }

    editAgendamento(id) {
        const agendamento = this.agendamentos.find(a => a.id === id);
        if (!agendamento) return;

        // Fechar modal de agendamentos do dia se estiver aberto
        const dayModal = document.getElementById('dayAgendamentosModal');
        if (dayModal) closeModal('dayAgendamentosModal');

        // Preencher formulário de edição
        const form = document.getElementById('novoAgendamentoForm');
        if (form) {
            form.querySelector('[name="cliente_id"]').value = agendamento.cliente_id;
            form.querySelector('[name="funcionario_id"]').value = agendamento.funcionario_id;
            form.querySelector('[name="produto_id"]').value = agendamento.produto_id;
            form.querySelector('[name="data_visita"]').value = agendamento.data_visita;
            form.querySelector('[name="hora_inicio"]').value = agendamento.hora_inicio;
            form.querySelector('[name="hora_fim"]').value = agendamento.hora_fim;
            form.querySelector('[name="endereco_visita"]').value = agendamento.endereco_visita || '';
            form.querySelector('[name="observacoes"]').value = agendamento.observacoes || '';
            form.querySelector('[name="valor"]').value = agendamento.valor;

            // Adicionar ID do agendamento para edição
            form.dataset.editingId = id;

            // Alterar título do modal
            const modalTitle = document.querySelector('#novoAgendamentoModal .modal-title');
            if (modalTitle) modalTitle.textContent = 'Editar Agendamento';

            openModal('novoAgendamentoModal');
        }
    }

    newAgendamentoForDate(dateStr) {
        // Fechar modal de agendamentos do dia
        closeModal('dayAgendamentosModal');

        // Preencher data no formulário
        const form = document.getElementById('novoAgendamentoForm');
        if (form) {
            form.querySelector('[name="data_visita"]').value = dateStr;
            form.reset();
            form.querySelector('[name="data_visita"]').value = dateStr; // Manter a data após reset
            delete form.dataset.editingId;

            // Restaurar título do modal
            const modalTitle = document.querySelector('#novoAgendamentoModal .modal-title');
            if (modalTitle) modalTitle.textContent = 'Novo Agendamento';

            openModal('novoAgendamentoModal');
        }
    }

    async updateStatus(id, status) {
        try {
            await api.updateAgendamentoStatus(id, status);

            // Atualizar agendamento local
            const agendamento = this.agendamentos.find(a => a.id === id);
            if (agendamento) {
                agendamento.status = status;
                this.render();
            }

            notifications.success(`Status atualizado para: ${this.getStatusLabel(status)}`);

            // Fechar modal e reabrir para mostrar mudanças
            closeModal('dayAgendamentosModal');

        } catch (error) {
            console.error('Erro ao atualizar status:', error);
            notifications.error('Erro ao atualizar status do agendamento');
        }
    }

    getStatusBadgeClass(status) {
        const statusMap = {
            'agendado': 'info',
            'confirmado': 'success',
            'em_andamento': 'warning',
            'concluido': 'success',
            'cancelado': 'error'
        };
        return statusMap[status] || 'info';
    }

    getStatusLabel(status) {
        const statusMap = {
            'agendado': 'Agendado',
            'confirmado': 'Confirmado',
            'em_andamento': 'Em Andamento',
            'concluido': 'Concluído',
            'cancelado': 'Cancelado'
        };
        return statusMap[status] || status;
    }

    // Método para adicionar novo agendamento ao calendário
    addAgendamento(agendamento) {
        this.agendamentos.push(agendamento);
        this.render();
    }

    // Método para atualizar agendamento no calendário
    updateAgendamento(id, updatedAgendamento) {
        const index = this.agendamentos.findIndex(a => a.id === id);
        if (index !== -1) {
            this.agendamentos[index] = { ...this.agendamentos[index], ...updatedAgendamento };
            this.render();
        }
    }

    // Método para remover agendamento do calendário
    removeAgendamento(id) {
        this.agendamentos = this.agendamentos.filter(a => a.id !== id);
        this.render();
    }
}

// Classe para mini calendário (seletor de data)
class MiniCalendar {
    constructor(inputId) {
        this.input = document.getElementById(inputId);
        this.currentDate = new Date();
        this.selectedDate = null;
        this.isOpen = false;
        
        this.monthNames = [
            'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
            'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
        ];
        
        this.dayNames = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S'];
        
        this.init();
    }

    init() {
        this.createCalendarElement();
        this.bindEvents();
    }

    createCalendarElement() {
        this.calendar = document.createElement('div');
        this.calendar.className = 'mini-calendar';
        this.calendar.style.display = 'none';
        
        this.input.parentNode.appendChild(this.calendar);
        this.render();
    }

    render() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        this.calendar.innerHTML = `
            <div class="mini-calendar-header">
                <button type="button" class="mini-calendar-nav" onclick="miniCalendar.previousMonth()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="mini-calendar-title">${this.monthNames[month]} ${year}</span>
                <button type="button" class="mini-calendar-nav" onclick="miniCalendar.nextMonth()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="mini-calendar-grid">
                ${this.renderDayHeaders()}
                ${this.renderDays()}
            </div>
        `;
    }

    renderDayHeaders() {
        return this.dayNames.map(day => 
            `<div class="mini-calendar-day-header">${day}</div>`
        ).join('');
    }

    renderDays() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        const days = [];
        const today = new Date();
        
        for (let i = 0; i < 42; i++) {
            const currentDay = new Date(startDate);
            currentDay.setDate(startDate.getDate() + i);
            
            const isCurrentMonth = currentDay.getMonth() === month;
            const isToday = currentDay.toDateString() === today.toDateString();
            const isSelected = this.selectedDate && currentDay.toDateString() === this.selectedDate.toDateString();
            
            let dayClass = 'mini-calendar-day';
            if (!isCurrentMonth) dayClass += ' other-month';
            if (isToday) dayClass += ' today';
            if (isSelected) dayClass += ' selected';
            
            days.push(`
                <div class="${dayClass}" onclick="miniCalendar.selectDate(new Date('${currentDay.toISOString()}'))">
                    ${currentDay.getDate()}
                </div>
            `);
        }
        
        return days.join('');
    }

    bindEvents() {
        this.input.addEventListener('focus', () => this.show());
        
        document.addEventListener('click', (e) => {
            if (!this.calendar.contains(e.target) && e.target !== this.input) {
                this.hide();
            }
        });
    }

    show() {
        this.calendar.style.display = 'block';
        this.isOpen = true;
    }

    hide() {
        this.calendar.style.display = 'none';
        this.isOpen = false;
    }

    selectDate(date) {
        this.selectedDate = date;
        this.input.value = date.toISOString().split('T')[0];
        this.render();
        this.hide();
        
        // Disparar evento de mudança
        this.input.dispatchEvent(new Event('change'));
    }

    previousMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        this.render();
    }

    nextMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        this.render();
    }
}

// Instância global do calendário (será inicializada no app.js)
let calendar;
