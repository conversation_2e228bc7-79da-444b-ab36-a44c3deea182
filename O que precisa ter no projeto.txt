👥 Tipos de Usuário (Permissões)
Tipo	Acesso
Admin	Total: gerencia usuários, vendas, produtos, agendamentos, relatórios
Gerente	Gerencia produtos, vendas e agendamentos, clientes
Vendedor	Apenas vendas, criar agendamentos, cadastrar novos clientes


🧩 Módulos do Sistema (Detalhados)

1. Login e Autenticação
Tela de login simples com e-mail e senha

Senha criptografada (com bcrypt.js)

Sessão mantida com localStorage

Redirecionamento com base nas permissões




2. Cadastro de Produtos (com cálculo automático de preço de venda)

Campos:
Nome

Descrição

Preço de custo

Lucro desejado (em R$ ou %, escolha automática com botão)

Preço final (calculado automaticamente)

Categoria

Estoque atual

Foto (opcional)

Funcionalidades:
Validação em tempo real

Exibição de cards bonitos ou tabelas interativas

Botão "Calcular preço" atualiza o valor final com base em custo + lucro

Controle de estoque (uso nas vendas)




3. Agendamentos de Visitas

Campos:
Nome do cliente

Telefone

Endereço completo (logradouro, número, bairro, cidade, UF)

Produto de interesse

Vendedor responsável (selecionável)

Data e hora

Observações

Funcionalidades:
Interface com calendário interativo (FullCalendar.js ou personalizado)

Validação de conflitos de horário

Edição e exclusão de agendamentos

Filtros por data, cliente, produto ou vendedor




4. Processo de Vendas (com desconto individual e total)

Etapas:
Selecionar cliente

Adicionar produtos (com preço final)

Inserir desconto por produto (em R$ ou %)

Inserir desconto na venda total (opcional, também R$ ou %)

Calcular subtotal, desconto e total

Finalizar venda

Gerar comprovante PDF

Funcionalidades:
Sistema de carrinho com controle de quantidade e desconto

Escolha entre desconto em valor fixo ou porcentagem

Atualização em tempo real do valor total

Geração de comprovante PDF moderno e personalizado (com jsPDF)

Layout do PDF:
Logo da empresa

Nome do cliente e vendedor

Lista de produtos com valores e descontos

Total final

Espaço para assinatura, data, e observações




5. Gerenciamento de Usuários

Campos:
Nome

E-mail

Tipo de usuário (admin, gerente, vendedor)

Senha (criptografada)

Funcionalidades:
Cadastro, edição, e remoção (somente por admins)

Controle de permissões por tipo

Visualização de atividades recentes (opcional)

Interface limpa e responsiva



🔐 Segurança e Boas Práticas

Senhas criptografadas (bcrypt)

Validação de dados em todos os formulários

Controle de sessões por localStorage + validação por permissões

Prevenção contra SQL injection (via prepared statements no SQLite)

Bloqueio de funções sensíveis para usuários sem permissão

Logs de atividade (via tabela de auditoria)



📊 Relatórios Úteis e Estratégicos para o Sistema
🔹 1. Relatório de Produtos com Estoque Baixo
Objetivo: Mostrar todos os produtos cujo estoque atual ≤ estoque mínimo.

Campos necessários no cadastro de produto:
estoque_atual (já existe)

estoque_minimo ✅ (novo campo a ser adicionado)

Exibição no relatório:
Produto	Estoque Atual	Estoque Mínimo	Categoria	Última Venda
Creme Relaxante	3	5	Bem-estar	05/06/2025

Funcionalidades:
Filtro por categoria

Ordenar por estoque restante (menor primeiro)

Botão "Enviar para compras" (simulação interna)

Destacar com cores produtos em situação crítica (ex: vermelho)

🔹 2. Relatório de Vendas por Período
Objetivo: Analisar desempenho financeiro da empresa em intervalos definidos.

Filtros disponíveis:
Período (data inicial e final)

Vendedor responsável

Produto específico (opcional)

Forma de pagamento (caso seja implementado)

Exibição no relatório:
Data	Cliente	Vendedor	Valor Total	Desconto	Total Final
03/06/2025	João Silva	Ana	R$ 250,00	R$ 10,00	R$ 240,00

Recursos:
Gráfico de barras por dia ou mês

Exportar para PDF ou imprimir

Total geral do período

🔹 3. Relatório de Produtos Mais Vendidos
Objetivo: Identificar quais produtos têm maior giro — útil para reabastecimento ou promoções.

Exibição:
Produto	Qtde Vendida	Receita Bruta	Categoria
Óleo Essencial	45	R$ 2.250,00	Bem-estar

Recursos:
Gráfico de pizza ou colunas

Filtro por período

Filtro por vendedor (para ver especialidades)

🔹 4. Relatório de Vendas por Vendedor
Objetivo: Avaliar desempenho da equipe.

Exibição:
Vendedor	Qtde de Vendas	Valor Total	Ticket Médio
Ana	14	R$ 4.350,00	R$ 310,71

Recursos:
Gráfico comparativo entre vendedores

Link para ver cada venda detalhadamente

🔹 5. Relatório de Lucro por Venda (ou por Produto)
Objetivo: Analisar o lucro real baseado no custo e descontos aplicados.

Necessário que cada produto tenha:

preco_custo

preco_venda

Descontos aplicados no momento da venda

Exibição:
Venda ID	Produto	Qtde	Custo Total	Receita	Lucro
#201	Chá Detox	2	R$ 30,00	R$ 50,00	R$ 20,00

Recursos:
Filtro por período

Filtro por vendedor

Filtro por produto

🔹 6. Relatório de Agendamentos por Período
Objetivo: Ver volume de atendimentos, prever demanda e evitar conflitos de agenda.

Filtros:
Período

Produto de interesse

Vendedor responsável

Exibição:
Data	Cliente	Produto	Vendedor	Status
10/06/2025	Maria Oliveira	Aparelho X	Ana	Confirmado

➕ Campos Adicionais para o Cadastro de Produtos
Para que tudo funcione como esperado, no cadastro de produtos, adicione os seguintes campos:

preco_custo ✅ (para calcular lucro real)

lucro_desejado (em R$ ou % — com seletor para tipo)

preco_venda (calculado com base no custo + lucro desejado)

estoque_minimo ✅ (para relatórios de estoque crítico)

categoria (opcional, mas útil para relatórios filtrados)

O sistema pode calcular preco_venda automaticamente ao digitar o lucro desejado, com base no tipo escolhido.