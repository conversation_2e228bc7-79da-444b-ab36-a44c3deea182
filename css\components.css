/* Componentes específicos */

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Calendário */
.calendar-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.calendar-day.selected {
    background: rgba(37, 99, 235, 0.2);
    border: 2px solid var(--primary-color);
}

.calendar-header {
    background: var(--primary-color);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-nav {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.calendar-nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.calendar-nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.calendar-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-day-header {
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    color: var(--gray-600);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.calendar-day {
    min-height: 120px;
    padding: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
    border-right: 1px solid var(--gray-200);
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.calendar-day:hover {
    background: var(--gray-50);
}

.calendar-day.other-month {
    color: var(--gray-400);
    background: var(--gray-50);
}

.calendar-day.today {
    background: rgba(37, 99, 235, 0.1);
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.calendar-event {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
    transition: var(--transition);
}

.calendar-event:hover {
    background: var(--primary-dark);
}

.calendar-event.status-confirmado {
    background: var(--success-color);
}

.calendar-event.status-cancelado {
    background: var(--error-color);
}

.calendar-event.status-concluido {
    background: var(--gray-600);
}

/* Filtros */
.filters-container {
    background: white;
    padding: 1.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

/* Notificações Avançadas */
#notifications-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 400px;
    pointer-events: none;
}

.notification {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    animation: slideInRight 0.3s ease;
    border-left: 4px solid var(--info-color);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--info-color);
    animation: notificationProgress 5s linear;
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.success::before {
    background: var(--success-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification.warning::before {
    background: var(--warning-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification.error::before {
    background: var(--error-color);
}

.notification-icon {
    font-size: 1.25rem;
    color: var(--info-color);
    margin-top: 0.125rem;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.error .notification-icon {
    color: var(--error-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.notification-message {
    color: var(--gray-600);
    font-size: 0.8rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-top: -0.25rem;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Loading */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-600);
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: white;
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.dropdown-toggle:hover {
    border-color: var(--gray-400);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.dropdown.open .dropdown-menu {
    display: block;
    animation: fadeIn 0.2s ease;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--gray-100);
}

.dropdown-item:hover {
    background: var(--gray-50);
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* Tabs */
.tabs {
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: 2rem;
}

.tabs-nav {
    display: flex;
    gap: 0;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-600);
    border-bottom: 2px solid transparent;
    transition: var(--transition);
}

.tab-button:hover {
    color: var(--gray-900);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Utilitários */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.d-flex { display: flex; }
.d-none { display: none; }
.d-block { display: block; }

.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-900);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

/* Progress Bar */
.progress {
    background: var(--gray-200);
    border-radius: 9999px;
    height: 8px;
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-color);
    height: 100%;
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.progress-bar.success {
    background: var(--success-color);
}

.progress-bar.warning {
    background: var(--warning-color);
}

.progress-bar.error {
    background: var(--error-color);
}

/* Switch/Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gray-300);
    transition: var(--transition);
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: var(--transition);
    border-radius: 50%;
}

.switch input:checked + .switch-slider {
    background: var(--primary-color);
}

.switch input:checked + .switch-slider:before {
    transform: translateX(24px);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.75rem;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    width: 100%;
}

/* Accordion */
.accordion-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    overflow: hidden;
}

.accordion-header {
    background: var(--gray-50);
    padding: 1rem 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.accordion-header:hover {
    background: var(--gray-100);
}

.accordion-title {
    font-weight: 600;
    color: var(--gray-900);
}

.accordion-icon {
    transition: transform 0.3s ease;
}

.accordion-item.open .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.accordion-item.open .accordion-content {
    max-height: 500px;
}

.accordion-body {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* Animações adicionais */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes notificationProgress {
    from { width: 100%; }
    to { width: 0%; }
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Kanban Board */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.kanban-column {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    min-height: 500px;
}

.kanban-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
}

.kanban-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-700);
}

.kanban-count {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.kanban-cards {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.kanban-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.kanban-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.kanban-card.status-agendado {
    border-left-color: var(--info-color);
}

.kanban-card.status-confirmado {
    border-left-color: var(--success-color);
}

.kanban-card.status-em_andamento {
    border-left-color: var(--warning-color);
}

.kanban-card.status-concluido {
    border-left-color: var(--gray-600);
}

.kanban-card.status-cancelado {
    border-left-color: var(--error-color);
    opacity: 0.7;
}

.kanban-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.kanban-card-title {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
    line-height: 1.3;
}

.kanban-card-time {
    font-size: 0.75rem;
    color: var(--gray-500);
    white-space: nowrap;
}

.kanban-card-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: var(--gray-600);
}

.kanban-card-value {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.kanban-card-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    opacity: 0;
    transition: var(--transition);
}

.kanban-card:hover .kanban-card-actions {
    opacity: 1;
}

/* Drag and Drop */
.kanban-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.kanban-column.drag-over {
    background: rgba(37, 99, 235, 0.1);
    border: 2px dashed var(--primary-color);
}

/* Search and Filter Results */
.search-results {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
}

.search-result-item:hover {
    background: var(--gray-50);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-highlight {
    background: yellow;
    font-weight: 600;
}

/* Export Button */
.export-options {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.export-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.export-option:hover {
    background: var(--gray-50);
}

/* Responsividade adicional */
@media (max-width: 768px) {
    #notifications-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .tooltip-content {
        position: fixed;
        bottom: auto;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .accordion-body {
        padding: 1rem;
    }

    .kanban-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .kanban-column {
        min-height: 300px;
    }

    .filters-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
