/* Componentes específicos */

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Calendário */
.calendar-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.calendar-day.selected {
    background: rgba(37, 99, 235, 0.2);
    border: 2px solid var(--primary-color);
}

.calendar-header {
    background: var(--primary-color);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-nav {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.calendar-nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.calendar-nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.calendar-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-day-header {
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    color: var(--gray-600);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.calendar-day {
    min-height: 120px;
    padding: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
    border-right: 1px solid var(--gray-200);
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.calendar-day:hover {
    background: var(--gray-50);
}

.calendar-day.other-month {
    color: var(--gray-400);
    background: var(--gray-50);
}

.calendar-day.today {
    background: rgba(37, 99, 235, 0.1);
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.calendar-event {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
    transition: var(--transition);
}

.calendar-event:hover {
    background: var(--primary-dark);
}

.calendar-event.status-confirmado {
    background: var(--success-color);
}

.calendar-event.status-cancelado {
    background: var(--error-color);
}

.calendar-event.status-concluido {
    background: var(--gray-600);
}

/* Filtros */
.filters-container {
    background: white;
    padding: 1.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

/* Notificações */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 1rem 1.5rem;
    max-width: 400px;
    z-index: 3000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.notification-message {
    font-weight: 500;
    color: var(--gray-900);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Loading */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-600);
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: white;
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.dropdown-toggle:hover {
    border-color: var(--gray-400);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.dropdown.open .dropdown-menu {
    display: block;
    animation: fadeIn 0.2s ease;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--gray-100);
}

.dropdown-item:hover {
    background: var(--gray-50);
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* Tabs */
.tabs {
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: 2rem;
}

.tabs-nav {
    display: flex;
    gap: 0;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-600);
    border-bottom: 2px solid transparent;
    transition: var(--transition);
}

.tab-button:hover {
    color: var(--gray-900);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Utilitários */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.d-flex { display: flex; }
.d-none { display: none; }
.d-block { display: block; }

.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }
